export default (initialState: any) => {
  // 在这里按照初始化数据定义项目中的权限，统一管理
  // 参考文档 https://umijs.org/docs/max/access
  const { currentUser, isAdmin } = initialState ?? {};

  console.log('initialState', initialState);
  return {
    // 基础权限：是否已登录
    isLoggedIn: !!currentUser,

    // 管理员权限：需要登录且是管理员角色
    canAdmin: isAdmin && currentUser,

    // 编辑权限：需要登录且是管理员或编辑员
    canEdit:
      currentUser &&
      (currentUser.role === 'admin' || currentUser.role === 'editor'),

    // 查看权限：已登录用户都可以查看
    canView: !!currentUser,

    // 用户管理权限：只有管理员可以管理用户
    canManageUsers: currentUser && currentUser.role === 'admin',

    // 字典管理权限：只有管理员可以管理字典
    canManageDictionary: currentUser && currentUser.role === 'admin',

    // 资源管理权限：管理员和编辑员都可以管理资源
    canManageResources:
      currentUser &&
      (currentUser.role === 'admin' || currentUser.role === 'editor'),
  };
};
